const jsrsasign = require('jsrsasign');
j = function(t) {
    for (var e = [], r = 0; r < t.length; r += 3) {
        for (var n = t[r] << 16 | t[r + 1] << 8 | t[r + 2], i = 0; i < 4; i++)
            8 * r + 6 * i <= 8 * t.length ? e.push("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(n >>> 6 * (3 - i) & 63)) : e.push("=")
    }
    return e.join("")
}
L = function(t) {
    return t ? j(function(t) {
        for (var e = new Uint8Array(t.length / 2), r = 0; r < t.length; r += 2)
            e[r / 2] = parseInt(t.substr(r, 2), 16);
        return e
    }(generatePublicKeyHex(t))) : ""
}
generatePublicKeyHex = function(publicKeyPem) {
    var strippedKey = publicKeyPem.replace(/-----BEGIN PUBLIC KEY-----|-----E<PERSON> PUBLIC KEY-----/g, '');
    var decodedKey = Buffer.from(strippedKey, 'base64');
    var hexStr = decodedKey.toString('hex').slice(52);
    return hexStr;
}
function get_bd_ticket_guard_ree_public_key(e) {

    bd_ticket_guard_ree_public_key = L(e)
    return bd_ticket_guard_ree_public_key
}
//////////////////////////获取cookies中的bd_ticket_guard_client_data///////////////////////////////////////////////////////////////////////////////////////////////

function get_cookies_bd_ticket_guard_client_data(bd_ticket_guard_ree_public_key) {
    cookies_bd_ticket_guard_client_data = btoa('{"bd-ticket-guard-version":2,"bd-ticket-guard-iteration-version":1,"bd-ticket-guard-ree-public-key":"' +bd_ticket_guard_ree_public_key + '","bd-ticket-guard-web-version":2}').replace(/=/g, '')+'%3D%3D'
    return cookies_bd_ticket_guard_client_data
}

//////////////////////////////////////////////////////////////////////////////////////////////////////////
var g = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
function y(e) {
    var t, n, r = "";
    for (t = 0; t + 3 <= e.length; t += 3)
        n = parseInt(e.substring(t, t + 3), 16),
        r += g.charAt(n >> 6) + g.charAt(63 & n);
    for (t + 1 == e.length ? (n = parseInt(e.substring(t, t + 1), 16),
    r += g.charAt(n << 2)) : t + 2 == e.length && (n = parseInt(e.substring(t, t + 2), 16),
    r += g.charAt(n >> 2) + g.charAt((3 & n) << 4)),
    "="; (3 & r.length) > 0; )
        r += "=";
    return r
}
////////////////////////获取headers中的bd_ticket_guard_client_data///////////////////////////
function get_headers_bd_ticket_guard_client_data(privateKeyPem,ticket,path,ts_sign,timestamp) {
    var t = 'ticket=' + ticket + '&path=' + path + '&timestamp=' + timestamp
    // 从 PEM 格式转换为 ASN.1 DER 格式的私钥
    const privateKey = jsrsasign.KEYUTIL.getKey(privateKeyPem);
    const dataToSign = t;
    // 进行签名
    const signature = new jsrsasign.KJUR.crypto.Signature({ alg: 'SHA256withECDSA' });
    signature.init(privateKey);
    signature.updateString(dataToSign);
    const signedData = signature.sign();
    console.log(signedData)
    var req_sign = y(signedData)
    console.log(req_sign)
    headers_bd_ticket_guard_client_data = btoa('{"ts_sign":"'+ ts_sign +'","req_content":"ticket,path,timestamp","req_sign":"' + req_sign + '","timestamp":' + timestamp + '}')
    return headers_bd_ticket_guard_client_data;
}
