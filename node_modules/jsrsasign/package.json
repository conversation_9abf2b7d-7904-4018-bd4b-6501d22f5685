{"name": "jsrsasign", "version": "11.1.0", "description": "opensource free pure JavaScript cryptographic library supports RSA/RSAPSS/ECDSA/DSA signing/validation, ASN.1, PKCS#1/5/8 private/public key, X.509 certificate, CRL, OCSP, CMS SignedData, TimeStamp and CAdES and JSON Web Signature(JWS)/Token(JWT)/Key(JWK).", "main": "lib/jsrsasign.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/kjur/jsrsasign.git"}, "keywords": ["crypto", "cryptography", "Cipher", "RSA", "ECDSA", "DSA", "RSAPSS", "PKCS#1", "PKCS#5", "PKCS#8", "private key", "public key", "CSR", "PKCS#10", "hash function", "HMac", "ASN.1", "certificate", "X.509", "CRL", "OCSP", "RFC 3161", "Digital Timestamp", "Timestamp", "Time Stamp Token", "CMS", "Cryptgraphic Message Syntax", "PKCS#7", "Signature", "Digital Signature", "signing", "Message Digest", "JSON Web Token", "JWT", "JSON Web Signature", "JWS", "JSON Web Key", "JWK", "JOSE", "JWA"], "author": "<PERSON><PERSON>", "homepage": "http://kjur.github.io/jsrsasign/", "license": "MIT", "bugs": {"url": "https://github.com/kjur/jsrsasign/issues"}, "dependencies": {}, "devDependencies": {"mocha": "*"}, "funding": "https://github.com/kjur/jsrsasign#donations", "badges": {"list": ["githubsponsors", "crypto"], "config": {"githubSponsorsUsername": "kjur", "cryptoURL": "https://github.com/kjur/jsrsasign#cryptocurrency"}}}