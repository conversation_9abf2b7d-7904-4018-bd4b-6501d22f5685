const assert = require('assert');
const rs = require('../lib/jsrsasign.js');

// RFC 9500 RSA1024 TEST PRIVATE KEY
let P5P = `**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`;

let prvobj = rs.KEYUTIL.getKey(P5P);

describe("KEYUTIL", function() {
    describe("getPEM", function() {
	it('encrypted private key PKCS8PRV', function() {
	    let head = "-----BEGIN ENCRYPTED PRIVATE KEY-----";
	    let prvencpem = rs.KEYUTIL.getPEM(prvobj, "PKCS8PRV", "passwd");
	    assert.equal(head, prvencpem.substr(0, head.length));
	});
    });
});

